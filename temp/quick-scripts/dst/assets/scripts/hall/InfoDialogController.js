
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/InfoDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '46d58hnp4tLf7B+lcQOkRXG', 'InfoDialogController');
// scripts/hall/InfoDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//游戏道具介绍页面
var InfoDialogController = /** @class */ (function (_super) {
    __extends(InfoDialogController, _super);
    function InfoDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        // 新增：单机规则和联机规则按钮
        _this.danjiLabel = null;
        _this.lianjiLabel = null;
        // 新增：单机和联机规则的ScrollView
        _this.danjiScrollView = null;
        _this.duorenScrollView = null;
        _this.infoItem = null;
        _this.infoItem1 = null;
        _this.infoImage1 = null;
        _this.infoImage2 = null;
        _this.infoImage3 = null;
        _this.infoImage4 = null;
        _this.infoImage5 = null;
        _this.infoImage6 = null;
        _this.infoImage7 = null;
        _this.infoImage8 = null;
        _this.infoImage9 = null;
        _this.infoImage10 = null;
        _this.infoImage11 = null;
        _this.titleList = []; //title 的列表
        _this.tipsList = [];
        _this.generationMethodList = [];
        _this.permanentList = [];
        _this.randomList = [];
        _this.chainsList = [];
        _this.iceList = [];
        _this.scoringDetails = [];
        _this.infoImageList = [];
        _this.backCallback = null; //隐藏弹窗的回调
        // 新增：当前选中的规则类型（0: 单机规则, 1: 联机规则）
        _this.currentRuleType = 0;
        // 新增：动画持续时间
        _this.animationDuration = 0.3;
        return _this;
        // update (dt) {}
    }
    InfoDialogController.prototype.onLoad = function () {
        //     this.infoImageList = [
        //         this.infoImage3,
        //         this.infoImage4,
        //         this.infoImage5,
        //         this.infoImage6,
        //         this.infoImage7,
        //         this.infoImage8,
        //         this.infoImage9,
        //         this.infoImage10,
        //         this.infoImage11,
        //     ]
        //     this.titleList = [
        //         window.getLocalizedStr('Tips'),
        //         window.getLocalizedStr('Generation_Method'),
        //         window.getLocalizedStr('Permanent_Task'),
        //         window.getLocalizedStr('Random_Task'),
        //         window.getLocalizedStr('Chains'),
        //         window.getLocalizedStr('Ice_Blocks'),
        //         window.getLocalizedStr('Scoring_Details'),
        //     ]//title 的列表
        //     this. tipsList = [
        //         window.getLocalizedStr('Tips1'),
        //         window.getLocalizedStr('Tips2'),
        //         window.getLocalizedStr('Tips3'),
        //         window.getLocalizedStr('Tips4'),
        //         window.getLocalizedStr('Tips5'),
        //     ]
        //     this.generationMethodList = [
        //         window.getLocalizedStr('Generation_Method1'),
        //         window.getLocalizedStr('Generation_Method2'),
        //         window.getLocalizedStr('Generation_Method3'),
        //         window.getLocalizedStr('Generation_Method4'),
        //         window.getLocalizedStr('Generation_Method5'),
        //         window.getLocalizedStr('Generation_Method6'),
        //         window.getLocalizedStr('Generation_Method7'),
        //         window.getLocalizedStr('Generation_Method8'),
        //         window.getLocalizedStr('Generation_Method9'),
        //     ]
        //     this.permanentList = [
        //         window.getLocalizedStr('Permanent_Task1'),
        //     ]
        //     this.randomList = [
        //         window.getLocalizedStr('Random_Task1'),
        //         window.getLocalizedStr('Random_Task2'),
        //         window.getLocalizedStr('Random_Task3'),
        //         window.getLocalizedStr('Random_Task4'),
        //         window.getLocalizedStr('Random_Task5'),
        //         window.getLocalizedStr('Random_Task6'),
        //         window.getLocalizedStr('Random_Task7'),
        //     ]
        //     this.chainsList = [
        //         window.getLocalizedStr('Chains1'),
        //         window.getLocalizedStr('Chains2'),
        //         window.getLocalizedStr('Chains3'),
        //     ]
        //     this.iceList = [
        //         window.getLocalizedStr('Ice_Blocks1'),
        //         window.getLocalizedStr('Ice_Blocks2'),
        //         window.getLocalizedStr('Ice_Blocks3'),
        //     ]
        //     this.scoringDetails = [
        //         window.getLocalizedStr('Scoring_Details1'),
        //         window.getLocalizedStr('Scoring_Details2'),
        //         window.getLocalizedStr('Scoring_Details3'),
        //         window.getLocalizedStr('Scoring_Details4'),
        //         window.getLocalizedStr('Scoring_Details5'),
        //         window.getLocalizedStr('Scoring_Details6'),
        //         window.getLocalizedStr('Scoring_Details7'),
        //         window.getLocalizedStr('Scoring_Details8'),
        //         window.getLocalizedStr('Scoring_Details9'),
        //         window.getLocalizedStr('Scoring_Details10'),
        //         window.getLocalizedStr('Scoring_Details11'),
        //     ]
    };
    InfoDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        // 新增：设置单机规则按钮点击事件
        if (this.danjiLabel) {
            Tools_1.Tools.setTouchEvent(this.danjiLabel, function () {
                _this.switchToRuleType(0); // 切换到单机规则
            });
        }
        // 新增：设置联机规则按钮点击事件
        if (this.lianjiLabel) {
            Tools_1.Tools.setTouchEvent(this.lianjiLabel, function () {
                _this.switchToRuleType(1); // 切换到联机规则
            });
        }
        // 新增：初始化显示状态（默认显示单机规则）
        this.initializeRuleDisplay();
        //     this.contentLay.removeAllChildren()
        //     this.getTitleNode(this.titleList[0])
        //     this.tipsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[1])
        //     this.generationMethodList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         let infoImg = cc.instantiate(this.infoImageList[index])
        //         infoItemOneController.setimgNode(infoImg)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[2])
        //     this.permanentList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg1 = cc.instantiate(this.infoImage1)
        //     this.contentLay.addChild(infoImg1)
        //     this.getTitleNode(this.titleList[3])
        //     this.randomList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg2 = cc.instantiate(this.infoImage2)
        //     this.contentLay.addChild(infoImg2)
        //     this.getTitleNode(this.titleList[4])
        //     this.chainsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[5])
        //     this.iceList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[6])
        //     this.scoringDetails.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        // }
        // getTitleNode(title: string) {
        //     let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体
        //     let infoItemController = infoItem.getComponent(InfoItemController)
        //     infoItemController.setContent(title)
        //     this.contentLay.addChild(infoItem)
        // }
    };
    /**
     * 新增：初始化规则显示状态
     */
    InfoDialogController.prototype.initializeRuleDisplay = function () {
        if (this.danjiScrollView && this.duorenScrollView) {
            // 默认显示单机规则，隐藏联机规则
            this.danjiScrollView.active = true;
            this.duorenScrollView.active = false;
            this.currentRuleType = 0;
            // 更新按钮状态
            this.updateButtonStates();
        }
    };
    /**
     * 新增：切换规则类型
     * @param ruleType 0: 单机规则, 1: 联机规则
     */
    InfoDialogController.prototype.switchToRuleType = function (ruleType) {
        if (this.currentRuleType === ruleType) {
            return; // 如果已经是当前类型，不需要切换
        }
        this.currentRuleType = ruleType;
        // 执行滑动切换动画
        this.performSlideAnimation(ruleType);
        // 更新按钮状态
        this.updateButtonStates();
    };
    /**
     * 新增：执行滑动切换动画
     * @param targetType 目标规则类型
     */
    InfoDialogController.prototype.performSlideAnimation = function (targetType) {
        if (!this.danjiScrollView || !this.duorenScrollView) {
            return;
        }
        var currentView = targetType === 0 ? this.duorenScrollView : this.danjiScrollView;
        var targetView = targetType === 0 ? this.danjiScrollView : this.duorenScrollView;
        // 设置目标视图的初始位置（从右侧或左侧滑入）
        var slideDistance = 600; // 滑动距离
        var slideDirection = targetType === 0 ? -1 : 1; // 0向左滑，1向右滑
        // 显示目标视图并设置初始位置
        targetView.active = true;
        targetView.x = slideDistance * slideDirection;
        // 同时执行两个动画：当前视图滑出，目标视图滑入
        cc.tween(currentView)
            .to(this.animationDuration, { x: -slideDistance * slideDirection }, { easing: 'quartOut' })
            .call(function () {
            currentView.active = false;
            currentView.x = 0; // 重置位置
        })
            .start();
        cc.tween(targetView)
            .to(this.animationDuration, { x: 0 }, { easing: 'quartOut' })
            .start();
    };
    /**
     * 新增：更新按钮状态
     */
    InfoDialogController.prototype.updateButtonStates = function () {
        if (!this.danjiLabel || !this.lianjiLabel) {
            return;
        }
        // 更新按钮颜色或透明度来表示选中状态
        if (this.currentRuleType === 0) {
            // 单机规则被选中
            this.danjiLabel.opacity = 255;
            this.lianjiLabel.opacity = 150;
        }
        else {
            // 联机规则被选中
            this.danjiLabel.opacity = 150;
            this.lianjiLabel.opacity = 255;
        }
    };
    InfoDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    InfoDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "danjiLabel", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "lianjiLabel", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "danjiScrollView", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "duorenScrollView", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage2", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage3", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage4", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage5", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage6", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage7", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage8", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage9", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage10", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage11", void 0);
    InfoDialogController = __decorate([
        ccclass
    ], InfoDialogController);
    return InfoDialogController;
}(cc.Component));
exports.default = InfoDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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