
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/InfoDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '46d58hnp4tLf7B+lcQOkRXG', 'InfoDialogController');
// scripts/hall/InfoDialogController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
//游戏道具介绍页面
var InfoDialogController = /** @class */ (function (_super) {
    __extends(InfoDialogController, _super);
    function InfoDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null;
        _this.boardBtnClose = null;
        _this.contentLay = null;
        // 新增：可移动的切换按钮
        _this.switchButton = null;
        // 新增：单机和联机规则的ScrollView
        _this.danjiScrollView = null;
        _this.duorenScrollView = null;
        _this.infoItem = null;
        _this.infoItem1 = null;
        _this.infoImage1 = null;
        _this.infoImage2 = null;
        _this.infoImage3 = null;
        _this.infoImage4 = null;
        _this.infoImage5 = null;
        _this.infoImage6 = null;
        _this.infoImage7 = null;
        _this.infoImage8 = null;
        _this.infoImage9 = null;
        _this.infoImage10 = null;
        _this.infoImage11 = null;
        _this.titleList = []; //title 的列表
        _this.tipsList = [];
        _this.generationMethodList = [];
        _this.permanentList = [];
        _this.randomList = [];
        _this.chainsList = [];
        _this.iceList = [];
        _this.scoringDetails = [];
        _this.infoImageList = [];
        _this.backCallback = null; //隐藏弹窗的回调
        // 新增：当前选中的规则类型（0: 单机规则, 1: 联机规则）
        _this.currentRuleType = 0;
        // 新增：动画持续时间
        _this.animationDuration = 0.3;
        return _this;
        // update (dt) {}
    }
    InfoDialogController.prototype.onLoad = function () {
        //     this.infoImageList = [
        //         this.infoImage3,
        //         this.infoImage4,
        //         this.infoImage5,
        //         this.infoImage6,
        //         this.infoImage7,
        //         this.infoImage8,
        //         this.infoImage9,
        //         this.infoImage10,
        //         this.infoImage11,
        //     ]
        //     this.titleList = [
        //         window.getLocalizedStr('Tips'),
        //         window.getLocalizedStr('Generation_Method'),
        //         window.getLocalizedStr('Permanent_Task'),
        //         window.getLocalizedStr('Random_Task'),
        //         window.getLocalizedStr('Chains'),
        //         window.getLocalizedStr('Ice_Blocks'),
        //         window.getLocalizedStr('Scoring_Details'),
        //     ]//title 的列表
        //     this. tipsList = [
        //         window.getLocalizedStr('Tips1'),
        //         window.getLocalizedStr('Tips2'),
        //         window.getLocalizedStr('Tips3'),
        //         window.getLocalizedStr('Tips4'),
        //         window.getLocalizedStr('Tips5'),
        //     ]
        //     this.generationMethodList = [
        //         window.getLocalizedStr('Generation_Method1'),
        //         window.getLocalizedStr('Generation_Method2'),
        //         window.getLocalizedStr('Generation_Method3'),
        //         window.getLocalizedStr('Generation_Method4'),
        //         window.getLocalizedStr('Generation_Method5'),
        //         window.getLocalizedStr('Generation_Method6'),
        //         window.getLocalizedStr('Generation_Method7'),
        //         window.getLocalizedStr('Generation_Method8'),
        //         window.getLocalizedStr('Generation_Method9'),
        //     ]
        //     this.permanentList = [
        //         window.getLocalizedStr('Permanent_Task1'),
        //     ]
        //     this.randomList = [
        //         window.getLocalizedStr('Random_Task1'),
        //         window.getLocalizedStr('Random_Task2'),
        //         window.getLocalizedStr('Random_Task3'),
        //         window.getLocalizedStr('Random_Task4'),
        //         window.getLocalizedStr('Random_Task5'),
        //         window.getLocalizedStr('Random_Task6'),
        //         window.getLocalizedStr('Random_Task7'),
        //     ]
        //     this.chainsList = [
        //         window.getLocalizedStr('Chains1'),
        //         window.getLocalizedStr('Chains2'),
        //         window.getLocalizedStr('Chains3'),
        //     ]
        //     this.iceList = [
        //         window.getLocalizedStr('Ice_Blocks1'),
        //         window.getLocalizedStr('Ice_Blocks2'),
        //         window.getLocalizedStr('Ice_Blocks3'),
        //     ]
        //     this.scoringDetails = [
        //         window.getLocalizedStr('Scoring_Details1'),
        //         window.getLocalizedStr('Scoring_Details2'),
        //         window.getLocalizedStr('Scoring_Details3'),
        //         window.getLocalizedStr('Scoring_Details4'),
        //         window.getLocalizedStr('Scoring_Details5'),
        //         window.getLocalizedStr('Scoring_Details6'),
        //         window.getLocalizedStr('Scoring_Details7'),
        //         window.getLocalizedStr('Scoring_Details8'),
        //         window.getLocalizedStr('Scoring_Details9'),
        //         window.getLocalizedStr('Scoring_Details10'),
        //         window.getLocalizedStr('Scoring_Details11'),
        //     ]
    };
    InfoDialogController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnClose, Config_1.Config.buttonRes + 'board_btn_close_normal', Config_1.Config.buttonRes + 'board_btn_close_pressed', function () {
            _this.hide();
        });
        // 新增：设置切换按钮点击事件
        if (this.switchButton) {
            Tools_1.Tools.setTouchEvent(this.switchButton, function () {
                _this.toggleRuleType(); // 切换规则类型
            });
        }
        // 新增：初始化显示状态（默认显示单机规则）
        this.initializeRuleDisplay();
        //     this.contentLay.removeAllChildren()
        //     this.getTitleNode(this.titleList[0])
        //     this.tipsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[1])
        //     this.generationMethodList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         let infoImg = cc.instantiate(this.infoImageList[index])
        //         infoItemOneController.setimgNode(infoImg)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[2])
        //     this.permanentList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg1 = cc.instantiate(this.infoImage1)
        //     this.contentLay.addChild(infoImg1)
        //     this.getTitleNode(this.titleList[3])
        //     this.randomList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     let infoImg2 = cc.instantiate(this.infoImage2)
        //     this.contentLay.addChild(infoImg2)
        //     this.getTitleNode(this.titleList[4])
        //     this.chainsList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[5])
        //     this.iceList.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        //     this.getTitleNode(this.titleList[6])
        //     this.scoringDetails.forEach((title, index) => {
        //         let infoItem = cc.instantiate(this.infoItem1);//初始化一个预制体
        //         let infoItemOneController = infoItem.getComponent(InfoItemOneController)
        //         infoItemOneController.setData(title)
        //         this.contentLay.addChild(infoItem)
        //     })
        // }
        // getTitleNode(title: string) {
        //     let infoItem = cc.instantiate(this.infoItem);//初始化一个预制体
        //     let infoItemController = infoItem.getComponent(InfoItemController)
        //     infoItemController.setContent(title)
        //     this.contentLay.addChild(infoItem)
        // }
    };
    /**
     * 新增：初始化规则显示状态
     */
    InfoDialogController.prototype.initializeRuleDisplay = function () {
        if (this.danjiScrollView && this.duorenScrollView) {
            // 默认显示单机规则，隐藏联机规则
            this.danjiScrollView.active = true;
            this.duorenScrollView.active = false;
            this.currentRuleType = 0;
            // 设置按钮初始位置（左边位置）
            if (this.switchButton) {
                this.switchButton.position = cc.v3(-150, -2, 0);
            }
        }
    };
    /**
     * 新增：切换规则类型（在单机和联机之间切换）
     */
    InfoDialogController.prototype.toggleRuleType = function () {
        // 切换到另一种类型
        this.currentRuleType = this.currentRuleType === 0 ? 1 : 0;
        console.log("\u5207\u6362\u5230: " + (this.currentRuleType === 0 ? '单机规则' : '联机规则'));
        // 移动按钮位置
        this.moveButtonToPosition();
        // 切换ScrollView显示
        this.switchScrollViewDisplay();
    };
    /**
     * 新增：移动按钮到指定位置
     */
    InfoDialogController.prototype.moveButtonToPosition = function () {
        if (!this.switchButton) {
            return;
        }
        // 按钮位置：左边（-150，-2）右边（142，-2）
        var leftPosition = cc.v3(-150, -2, 0);
        var rightPosition = cc.v3(142, -2, 0);
        var targetPosition = this.currentRuleType === 0 ? leftPosition : rightPosition;
        // 使用动画移动按钮
        cc.tween(this.switchButton)
            .to(0.3, { position: targetPosition }, { easing: 'quartOut' })
            .start();
    };
    /**
     * 新增：切换ScrollView显示
     */
    InfoDialogController.prototype.switchScrollViewDisplay = function () {
        if (!this.danjiScrollView || !this.duorenScrollView) {
            return;
        }
        if (this.currentRuleType === 0) {
            // 显示单机规则
            this.danjiScrollView.active = true;
            this.duorenScrollView.active = false;
        }
        else {
            // 显示联机规则
            this.danjiScrollView.active = false;
            this.duorenScrollView.active = true;
        }
    };
    InfoDialogController.prototype.show = function (backCallback) {
        this.backCallback = backCallback;
        this.node.active = true;
        this.boardBg.scale = 0;
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 1 })
            .start();
    };
    InfoDialogController.prototype.hide = function () {
        var _this = this;
        if (this.backCallback) {
            this.backCallback();
        }
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, { scale: 0 })
            .call(function () {
            _this.node.active = false;
        })
            .start();
    };
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "boardBtnClose", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "contentLay", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "switchButton", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "danjiScrollView", void 0);
    __decorate([
        property(cc.Node)
    ], InfoDialogController.prototype, "duorenScrollView", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoItem1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage1", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage2", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage3", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage4", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage5", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage6", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage7", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage8", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage9", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage10", void 0);
    __decorate([
        property(cc.Prefab)
    ], InfoDialogController.prototype, "infoImage11", void 0);
    InfoDialogController = __decorate([
        ccclass
    ], InfoDialogController);
    return InfoDialogController;
}(cc.Component));
exports.default = InfoDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2hhbGwvSW5mb0RpYWxvZ0NvbnRyb2xsZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLG9CQUFvQjtBQUNwQiw0RUFBNEU7QUFDNUUsbUJBQW1CO0FBQ25CLHNGQUFzRjtBQUN0Riw4QkFBOEI7QUFDOUIsc0ZBQXNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLdEYseUNBQXdDO0FBQ3hDLHVDQUFzQztBQUVoQyxJQUFBLEtBQXdCLEVBQUUsQ0FBQyxVQUFVLEVBQW5DLE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBa0IsQ0FBQztBQUc1QyxVQUFVO0FBRVY7SUFBa0Qsd0NBQVk7SUFBOUQ7UUFBQSxxRUErVUM7UUE1VUcsYUFBTyxHQUFZLElBQUksQ0FBQTtRQUV2QixtQkFBYSxHQUFZLElBQUksQ0FBQTtRQUU3QixnQkFBVSxHQUFZLElBQUksQ0FBQTtRQUUxQixjQUFjO1FBRWQsa0JBQVksR0FBWSxJQUFJLENBQUE7UUFFNUIsd0JBQXdCO1FBRXhCLHFCQUFlLEdBQVksSUFBSSxDQUFBO1FBRS9CLHNCQUFnQixHQUFZLElBQUksQ0FBQTtRQUVoQyxjQUFRLEdBQWMsSUFBSSxDQUFDO1FBRTNCLGVBQVMsR0FBYyxJQUFJLENBQUM7UUFHNUIsZ0JBQVUsR0FBYyxJQUFJLENBQUE7UUFFNUIsZ0JBQVUsR0FBYyxJQUFJLENBQUE7UUFFNUIsZ0JBQVUsR0FBYyxJQUFJLENBQUE7UUFFNUIsZ0JBQVUsR0FBYyxJQUFJLENBQUE7UUFFNUIsZ0JBQVUsR0FBYyxJQUFJLENBQUE7UUFFNUIsZ0JBQVUsR0FBYyxJQUFJLENBQUE7UUFFNUIsZ0JBQVUsR0FBYyxJQUFJLENBQUE7UUFFNUIsZ0JBQVUsR0FBYyxJQUFJLENBQUE7UUFFNUIsZ0JBQVUsR0FBYyxJQUFJLENBQUE7UUFFNUIsaUJBQVcsR0FBYyxJQUFJLENBQUE7UUFFN0IsaUJBQVcsR0FBYyxJQUFJLENBQUE7UUFFN0IsZUFBUyxHQUFhLEVBQUUsQ0FBQSxDQUFBLFdBQVc7UUFDbkMsY0FBUSxHQUFhLEVBQUUsQ0FBQTtRQUN2QiwwQkFBb0IsR0FBYSxFQUFFLENBQUE7UUFDbkMsbUJBQWEsR0FBYSxFQUFFLENBQUE7UUFDNUIsZ0JBQVUsR0FBYSxFQUFFLENBQUE7UUFDekIsZ0JBQVUsR0FBYSxFQUFFLENBQUE7UUFDekIsYUFBTyxHQUFhLEVBQUUsQ0FBQTtRQUN0QixvQkFBYyxHQUFhLEVBQUUsQ0FBQTtRQUU3QixtQkFBYSxHQUFnQixFQUFFLENBQUE7UUFHL0Isa0JBQVksR0FBYSxJQUFJLENBQUEsQ0FBQyxTQUFTO1FBRXZDLGlDQUFpQztRQUN6QixxQkFBZSxHQUFXLENBQUMsQ0FBQTtRQUVuQyxZQUFZO1FBQ0osdUJBQWlCLEdBQVcsR0FBRyxDQUFBOztRQThRdkMsaUJBQWlCO0lBQ3JCLENBQUM7SUE3UUcscUNBQU0sR0FBTjtRQUVBLDZCQUE2QjtRQUM3QiwyQkFBMkI7UUFDM0IsMkJBQTJCO1FBQzNCLDJCQUEyQjtRQUMzQiwyQkFBMkI7UUFDM0IsMkJBQTJCO1FBQzNCLDJCQUEyQjtRQUMzQiwyQkFBMkI7UUFDM0IsNEJBQTRCO1FBQzVCLDRCQUE0QjtRQUM1QixRQUFRO1FBR1IseUJBQXlCO1FBQ3pCLDBDQUEwQztRQUMxQyx1REFBdUQ7UUFDdkQsb0RBQW9EO1FBQ3BELGlEQUFpRDtRQUNqRCw0Q0FBNEM7UUFDNUMsZ0RBQWdEO1FBQ2hELHFEQUFxRDtRQUNyRCxtQkFBbUI7UUFDbkIseUJBQXlCO1FBQ3pCLDJDQUEyQztRQUMzQywyQ0FBMkM7UUFDM0MsMkNBQTJDO1FBQzNDLDJDQUEyQztRQUMzQywyQ0FBMkM7UUFDM0MsUUFBUTtRQUNSLG9DQUFvQztRQUNwQyx3REFBd0Q7UUFDeEQsd0RBQXdEO1FBQ3hELHdEQUF3RDtRQUN4RCx3REFBd0Q7UUFDeEQsd0RBQXdEO1FBQ3hELHdEQUF3RDtRQUN4RCx3REFBd0Q7UUFDeEQsd0RBQXdEO1FBQ3hELHdEQUF3RDtRQUN4RCxRQUFRO1FBQ1IsNkJBQTZCO1FBQzdCLHFEQUFxRDtRQUNyRCxRQUFRO1FBQ1IsMEJBQTBCO1FBQzFCLGtEQUFrRDtRQUNsRCxrREFBa0Q7UUFDbEQsa0RBQWtEO1FBQ2xELGtEQUFrRDtRQUNsRCxrREFBa0Q7UUFDbEQsa0RBQWtEO1FBQ2xELGtEQUFrRDtRQUNsRCxRQUFRO1FBQ1IsMEJBQTBCO1FBQzFCLDZDQUE2QztRQUM3Qyw2Q0FBNkM7UUFDN0MsNkNBQTZDO1FBQzdDLFFBQVE7UUFDUix1QkFBdUI7UUFDdkIsaURBQWlEO1FBQ2pELGlEQUFpRDtRQUNqRCxpREFBaUQ7UUFDakQsUUFBUTtRQUNSLDhCQUE4QjtRQUM5QixzREFBc0Q7UUFDdEQsc0RBQXNEO1FBQ3RELHNEQUFzRDtRQUN0RCxzREFBc0Q7UUFDdEQsc0RBQXNEO1FBQ3RELHNEQUFzRDtRQUN0RCxzREFBc0Q7UUFDdEQsc0RBQXNEO1FBQ3RELHNEQUFzRDtRQUN0RCx1REFBdUQ7UUFDdkQsdURBQXVEO1FBQ3ZELFFBQVE7SUFJUixDQUFDO0lBRUQsb0NBQUssR0FBTDtRQUFBLGlCQXdGQztRQXRGRyxhQUFLLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxlQUFNLENBQUMsU0FBUyxHQUFHLHdCQUF3QixFQUFFLGVBQU0sQ0FBQyxTQUFTLEdBQUcseUJBQXlCLEVBQUU7WUFDbEksS0FBSSxDQUFDLElBQUksRUFBRSxDQUFBO1FBQ2YsQ0FBQyxDQUFDLENBQUM7UUFFSCxnQkFBZ0I7UUFDaEIsSUFBSSxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ25CLGFBQUssQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLFlBQVksRUFBRTtnQkFDbkMsS0FBSSxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUMsU0FBUztZQUNwQyxDQUFDLENBQUMsQ0FBQztTQUNOO1FBRUQsdUJBQXVCO1FBQ3ZCLElBQUksQ0FBQyxxQkFBcUIsRUFBRSxDQUFDO1FBRWpDLDBDQUEwQztRQUUxQywyQ0FBMkM7UUFDM0MsZ0RBQWdEO1FBQ2hELG1FQUFtRTtRQUNuRSxtRkFBbUY7UUFDbkYsK0NBQStDO1FBQy9DLDZDQUE2QztRQUM3QyxTQUFTO1FBRVQsMkNBQTJDO1FBQzNDLDREQUE0RDtRQUM1RCxtRUFBbUU7UUFDbkUsbUZBQW1GO1FBQ25GLCtDQUErQztRQUUvQyxrRUFBa0U7UUFDbEUsb0RBQW9EO1FBRXBELDZDQUE2QztRQUM3QyxTQUFTO1FBQ1QsMkNBQTJDO1FBQzNDLHFEQUFxRDtRQUNyRCxtRUFBbUU7UUFDbkUsbUZBQW1GO1FBQ25GLCtDQUErQztRQUMvQyw2Q0FBNkM7UUFDN0MsU0FBUztRQUNULHFEQUFxRDtRQUNyRCx5Q0FBeUM7UUFHekMsMkNBQTJDO1FBQzNDLGtEQUFrRDtRQUNsRCxtRUFBbUU7UUFDbkUsbUZBQW1GO1FBQ25GLCtDQUErQztRQUMvQyw2Q0FBNkM7UUFDN0MsU0FBUztRQUNULHFEQUFxRDtRQUNyRCx5Q0FBeUM7UUFFekMsMkNBQTJDO1FBQzNDLGtEQUFrRDtRQUNsRCxtRUFBbUU7UUFDbkUsbUZBQW1GO1FBQ25GLCtDQUErQztRQUMvQyw2Q0FBNkM7UUFDN0MsU0FBUztRQUNULDJDQUEyQztRQUMzQywrQ0FBK0M7UUFDL0MsbUVBQW1FO1FBQ25FLG1GQUFtRjtRQUNuRiwrQ0FBK0M7UUFDL0MsNkNBQTZDO1FBQzdDLFNBQVM7UUFDVCwyQ0FBMkM7UUFDM0Msc0RBQXNEO1FBQ3RELG1FQUFtRTtRQUNuRSxtRkFBbUY7UUFDbkYsK0NBQStDO1FBQy9DLDZDQUE2QztRQUM3QyxTQUFTO1FBQ1QsSUFBSTtRQUdKLGdDQUFnQztRQUNoQyw4REFBOEQ7UUFDOUQseUVBQXlFO1FBQ3pFLDJDQUEyQztRQUMzQyx5Q0FBeUM7UUFDekMsSUFBSTtJQUNKLENBQUM7SUFFRDs7T0FFRztJQUNLLG9EQUFxQixHQUE3QjtRQUNJLElBQUksSUFBSSxDQUFDLGVBQWUsSUFBSSxJQUFJLENBQUMsZ0JBQWdCLEVBQUU7WUFDL0Msa0JBQWtCO1lBQ2xCLElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQztZQUNuQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztZQUNyQyxJQUFJLENBQUMsZUFBZSxHQUFHLENBQUMsQ0FBQztZQUV6QixpQkFBaUI7WUFDakIsSUFBSSxJQUFJLENBQUMsWUFBWSxFQUFFO2dCQUNuQixJQUFJLENBQUMsWUFBWSxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO2FBQ25EO1NBQ0o7SUFDTCxDQUFDO0lBRUQ7O09BRUc7SUFDSyw2Q0FBYyxHQUF0QjtRQUNJLFdBQVc7UUFDWCxJQUFJLENBQUMsZUFBZSxHQUFHLElBQUksQ0FBQyxlQUFlLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUUxRCxPQUFPLENBQUMsR0FBRyxDQUFDLDBCQUFRLElBQUksQ0FBQyxlQUFlLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBRSxDQUFDLENBQUM7UUFFcEUsU0FBUztRQUNULElBQUksQ0FBQyxvQkFBb0IsRUFBRSxDQUFDO1FBRTVCLGlCQUFpQjtRQUNqQixJQUFJLENBQUMsdUJBQXVCLEVBQUUsQ0FBQztJQUNuQyxDQUFDO0lBRUQ7O09BRUc7SUFDSyxtREFBb0IsR0FBNUI7UUFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksRUFBRTtZQUNwQixPQUFPO1NBQ1Y7UUFFRCw2QkFBNkI7UUFDN0IsSUFBTSxZQUFZLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUN4QyxJQUFNLGFBQWEsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUV4QyxJQUFNLGNBQWMsR0FBRyxJQUFJLENBQUMsZUFBZSxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxhQUFhLENBQUM7UUFFakYsV0FBVztRQUNYLEVBQUUsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQzthQUN0QixFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsUUFBUSxFQUFFLGNBQWMsRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLFVBQVUsRUFBRSxDQUFDO2FBQzdELEtBQUssRUFBRSxDQUFDO0lBQ2pCLENBQUM7SUFFRDs7T0FFRztJQUNLLHNEQUF1QixHQUEvQjtRQUNJLElBQUksQ0FBQyxJQUFJLENBQUMsZUFBZSxJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFO1lBQ2pELE9BQU87U0FDVjtRQUVELElBQUksSUFBSSxDQUFDLGVBQWUsS0FBSyxDQUFDLEVBQUU7WUFDNUIsU0FBUztZQUNULElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQztZQUNuQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztTQUN4QzthQUFNO1lBQ0gsU0FBUztZQUNULElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztZQUNwQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQztTQUN2QztJQUNMLENBQUM7SUFJRCxtQ0FBSSxHQUFKLFVBQUssWUFBc0I7UUFDdkIsSUFBSSxDQUFDLFlBQVksR0FBRyxZQUFZLENBQUE7UUFDaEMsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFBO1FBQ3ZCLElBQUksQ0FBQyxPQUFPLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQTtRQUN0QixTQUFTO1FBQ1QsRUFBRSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDO2FBQ2pCLEVBQUUsQ0FBQyxlQUFNLENBQUMsZUFBZSxFQUFFLEVBQUUsS0FBSyxFQUFFLENBQUMsRUFBRSxDQUFDO2FBQ3hDLEtBQUssRUFBRSxDQUFDO0lBQ2pCLENBQUM7SUFDRCxtQ0FBSSxHQUFKO1FBQUEsaUJBV0M7UUFWRyxJQUFJLElBQUksQ0FBQyxZQUFZLEVBQUU7WUFDbkIsSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFBO1NBQ3RCO1FBQ0QsU0FBUztRQUNULEVBQUUsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQzthQUNqQixFQUFFLENBQUMsZUFBTSxDQUFDLGVBQWUsRUFBRSxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUUsQ0FBQzthQUN4QyxJQUFJLENBQUM7WUFDRixLQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7UUFDNUIsQ0FBQyxDQUFDO2FBQ0QsS0FBSyxFQUFFLENBQUM7SUFDakIsQ0FBQztJQXpVRDtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDO3lEQUNLO0lBRXZCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7K0RBQ1c7SUFFN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs0REFDUTtJQUkxQjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzhEQUNVO0lBSTVCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7aUVBQ2E7SUFFL0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQztrRUFDYztJQUVoQztRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOzBEQUNPO0lBRTNCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7MkRBQ1E7SUFHNUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzs0REFDUTtJQUU1QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOzREQUNRO0lBRTVCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7NERBQ1E7SUFFNUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzs0REFDUTtJQUU1QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOzREQUNRO0lBRTVCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7NERBQ1E7SUFFNUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzs0REFDUTtJQUU1QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOzREQUNRO0lBRTVCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7NERBQ1E7SUFFNUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzs2REFDUztJQUU3QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDOzZEQUNTO0lBNUNaLG9CQUFvQjtRQUR4QyxPQUFPO09BQ2Esb0JBQW9CLENBK1V4QztJQUFELDJCQUFDO0NBL1VELEFBK1VDLENBL1VpRCxFQUFFLENBQUMsU0FBUyxHQStVN0Q7a0JBL1VvQixvQkFBb0IiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMZWFybiBUeXBlU2NyaXB0OlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3R5cGVzY3JpcHQuaHRtbFxuLy8gTGVhcm4gQXR0cmlidXRlOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3JlZmVyZW5jZS9hdHRyaWJ1dGVzLmh0bWxcbi8vIExlYXJuIGxpZmUtY3ljbGUgY2FsbGJhY2tzOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL2xpZmUtY3ljbGUtY2FsbGJhY2tzLmh0bWxcblxuXG5pbXBvcnQgSW5mb0l0ZW1Db250cm9sbGVyIGZyb20gXCIuLi9wZmIvSW5mb0l0ZW1Db250cm9sbGVyXCI7XG5pbXBvcnQgSW5mb0l0ZW1PbmVDb250cm9sbGVyIGZyb20gXCIuLi9wZmIvSW5mb0l0ZW1PbmVDb250cm9sbGVyXCI7XG5pbXBvcnQgeyBDb25maWcgfSBmcm9tIFwiLi4vdXRpbC9Db25maWdcIjtcbmltcG9ydCB7IFRvb2xzIH0gZnJvbSBcIi4uL3V0aWwvVG9vbHNcIjtcblxuY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSB9ID0gY2MuX2RlY29yYXRvcjtcblxuXG4vL+a4uOaIj+mBk+WFt+S7i+e7jemhtemdolxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEluZm9EaWFsb2dDb250cm9sbGVyIGV4dGVuZHMgY2MuQ29tcG9uZW50IHtcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGJvYXJkQmc6IGNjLk5vZGUgPSBudWxsXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgYm9hcmRCdG5DbG9zZTogY2MuTm9kZSA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBjb250ZW50TGF5OiBjYy5Ob2RlID0gbnVsbFxuXG4gICAgLy8g5paw5aKe77ya5Y+v56e75Yqo55qE5YiH5o2i5oyJ6ZKuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgc3dpdGNoQnV0dG9uOiBjYy5Ob2RlID0gbnVsbFxuXG4gICAgLy8g5paw5aKe77ya5Y2V5py65ZKM6IGU5py66KeE5YiZ55qEU2Nyb2xsVmlld1xuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGRhbmppU2Nyb2xsVmlldzogY2MuTm9kZSA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBkdW9yZW5TY3JvbGxWaWV3OiBjYy5Ob2RlID0gbnVsbFxuICAgIEBwcm9wZXJ0eShjYy5QcmVmYWIpXG4gICAgaW5mb0l0ZW06IGNjLlByZWZhYiA9IG51bGw7XG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBpbmZvSXRlbTE6IGNjLlByZWZhYiA9IG51bGw7XG5cbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGluZm9JbWFnZTE6IGNjLlByZWZhYiA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGluZm9JbWFnZTI6IGNjLlByZWZhYiA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGluZm9JbWFnZTM6IGNjLlByZWZhYiA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGluZm9JbWFnZTQ6IGNjLlByZWZhYiA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGluZm9JbWFnZTU6IGNjLlByZWZhYiA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGluZm9JbWFnZTY6IGNjLlByZWZhYiA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGluZm9JbWFnZTc6IGNjLlByZWZhYiA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGluZm9JbWFnZTg6IGNjLlByZWZhYiA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGluZm9JbWFnZTk6IGNjLlByZWZhYiA9IG51bGxcbiAgICBAcHJvcGVydHkoY2MuUHJlZmFiKVxuICAgIGluZm9JbWFnZTEwOiBjYy5QcmVmYWIgPSBudWxsXG4gICAgQHByb3BlcnR5KGNjLlByZWZhYilcbiAgICBpbmZvSW1hZ2UxMTogY2MuUHJlZmFiID0gbnVsbFxuXG4gICAgdGl0bGVMaXN0OiBzdHJpbmdbXSA9IFtdLy90aXRsZSDnmoTliJfooahcbiAgICB0aXBzTGlzdDogc3RyaW5nW10gPSBbXVxuICAgIGdlbmVyYXRpb25NZXRob2RMaXN0OiBzdHJpbmdbXSA9IFtdXG4gICAgcGVybWFuZW50TGlzdDogc3RyaW5nW10gPSBbXVxuICAgIHJhbmRvbUxpc3Q6IHN0cmluZ1tdID0gW11cbiAgICBjaGFpbnNMaXN0OiBzdHJpbmdbXSA9IFtdXG4gICAgaWNlTGlzdDogc3RyaW5nW10gPSBbXVxuICAgIHNjb3JpbmdEZXRhaWxzOiBzdHJpbmdbXSA9IFtdXG5cbiAgICBpbmZvSW1hZ2VMaXN0OiBjYy5QcmVmYWJbXSA9IFtdXG5cblxuICAgIGJhY2tDYWxsYmFjazogRnVuY3Rpb24gPSBudWxsIC8v6ZqQ6JeP5by556qX55qE5Zue6LCDXG5cbiAgICAvLyDmlrDlop7vvJrlvZPliY3pgInkuK3nmoTop4TliJnnsbvlnovvvIgwOiDljZXmnLrop4TliJksIDE6IOiBlOacuuinhOWIme+8iVxuICAgIHByaXZhdGUgY3VycmVudFJ1bGVUeXBlOiBudW1iZXIgPSAwXG5cbiAgICAvLyDmlrDlop7vvJrliqjnlLvmjIHnu63ml7bpl7RcbiAgICBwcml2YXRlIGFuaW1hdGlvbkR1cmF0aW9uOiBudW1iZXIgPSAwLjNcblxuICAgIG9uTG9hZCgpIHtcblxuICAgIC8vICAgICB0aGlzLmluZm9JbWFnZUxpc3QgPSBbXG4gICAgLy8gICAgICAgICB0aGlzLmluZm9JbWFnZTMsXG4gICAgLy8gICAgICAgICB0aGlzLmluZm9JbWFnZTQsXG4gICAgLy8gICAgICAgICB0aGlzLmluZm9JbWFnZTUsXG4gICAgLy8gICAgICAgICB0aGlzLmluZm9JbWFnZTYsXG4gICAgLy8gICAgICAgICB0aGlzLmluZm9JbWFnZTcsXG4gICAgLy8gICAgICAgICB0aGlzLmluZm9JbWFnZTgsXG4gICAgLy8gICAgICAgICB0aGlzLmluZm9JbWFnZTksXG4gICAgLy8gICAgICAgICB0aGlzLmluZm9JbWFnZTEwLFxuICAgIC8vICAgICAgICAgdGhpcy5pbmZvSW1hZ2UxMSxcbiAgICAvLyAgICAgXVxuXG5cbiAgICAvLyAgICAgdGhpcy50aXRsZUxpc3QgPSBbXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdUaXBzJyksXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdHZW5lcmF0aW9uX01ldGhvZCcpLFxuICAgIC8vICAgICAgICAgd2luZG93LmdldExvY2FsaXplZFN0cignUGVybWFuZW50X1Rhc2snKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1JhbmRvbV9UYXNrJyksXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdDaGFpbnMnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ0ljZV9CbG9ja3MnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlscycpLFxuICAgIC8vICAgICBdLy90aXRsZSDnmoTliJfooahcbiAgICAvLyAgICAgdGhpcy4gdGlwc0xpc3QgPSBbXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdUaXBzMScpLFxuICAgIC8vICAgICAgICAgd2luZG93LmdldExvY2FsaXplZFN0cignVGlwczInKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1RpcHMzJyksXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdUaXBzNCcpLFxuICAgIC8vICAgICAgICAgd2luZG93LmdldExvY2FsaXplZFN0cignVGlwczUnKSxcbiAgICAvLyAgICAgXVxuICAgIC8vICAgICB0aGlzLmdlbmVyYXRpb25NZXRob2RMaXN0ID0gW1xuICAgIC8vICAgICAgICAgd2luZG93LmdldExvY2FsaXplZFN0cignR2VuZXJhdGlvbl9NZXRob2QxJyksXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdHZW5lcmF0aW9uX01ldGhvZDInKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ0dlbmVyYXRpb25fTWV0aG9kMycpLFxuICAgIC8vICAgICAgICAgd2luZG93LmdldExvY2FsaXplZFN0cignR2VuZXJhdGlvbl9NZXRob2Q0JyksXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdHZW5lcmF0aW9uX01ldGhvZDUnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ0dlbmVyYXRpb25fTWV0aG9kNicpLFxuICAgIC8vICAgICAgICAgd2luZG93LmdldExvY2FsaXplZFN0cignR2VuZXJhdGlvbl9NZXRob2Q3JyksXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdHZW5lcmF0aW9uX01ldGhvZDgnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ0dlbmVyYXRpb25fTWV0aG9kOScpLFxuICAgIC8vICAgICBdXG4gICAgLy8gICAgIHRoaXMucGVybWFuZW50TGlzdCA9IFtcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Blcm1hbmVudF9UYXNrMScpLFxuICAgIC8vICAgICBdXG4gICAgLy8gICAgIHRoaXMucmFuZG9tTGlzdCA9IFtcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1JhbmRvbV9UYXNrMScpLFxuICAgIC8vICAgICAgICAgd2luZG93LmdldExvY2FsaXplZFN0cignUmFuZG9tX1Rhc2syJyksXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdSYW5kb21fVGFzazMnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1JhbmRvbV9UYXNrNCcpLFxuICAgIC8vICAgICAgICAgd2luZG93LmdldExvY2FsaXplZFN0cignUmFuZG9tX1Rhc2s1JyksXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdSYW5kb21fVGFzazYnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1JhbmRvbV9UYXNrNycpLFxuICAgIC8vICAgICBdXG4gICAgLy8gICAgIHRoaXMuY2hhaW5zTGlzdCA9IFtcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ0NoYWluczEnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ0NoYWluczInKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ0NoYWluczMnKSxcbiAgICAvLyAgICAgXVxuICAgIC8vICAgICB0aGlzLmljZUxpc3QgPSBbXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdJY2VfQmxvY2tzMScpLFxuICAgIC8vICAgICAgICAgd2luZG93LmdldExvY2FsaXplZFN0cignSWNlX0Jsb2NrczInKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ0ljZV9CbG9ja3MzJyksXG4gICAgLy8gICAgIF1cbiAgICAvLyAgICAgdGhpcy5zY29yaW5nRGV0YWlscyA9IFtcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlsczEnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlsczInKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlsczMnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlsczQnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlsczUnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlsczYnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlsczcnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlsczgnKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlsczknKSxcbiAgICAvLyAgICAgICAgIHdpbmRvdy5nZXRMb2NhbGl6ZWRTdHIoJ1Njb3JpbmdfRGV0YWlsczEwJyksXG4gICAgLy8gICAgICAgICB3aW5kb3cuZ2V0TG9jYWxpemVkU3RyKCdTY29yaW5nX0RldGFpbHMxMScpLFxuICAgIC8vICAgICBdXG5cblxuXG4gICAgfVxuXG4gICAgc3RhcnQoKSB7XG5cbiAgICAgICAgVG9vbHMuaW1hZ2VCdXR0b25DbGljayh0aGlzLmJvYXJkQnRuQ2xvc2UsIENvbmZpZy5idXR0b25SZXMgKyAnYm9hcmRfYnRuX2Nsb3NlX25vcm1hbCcsIENvbmZpZy5idXR0b25SZXMgKyAnYm9hcmRfYnRuX2Nsb3NlX3ByZXNzZWQnLCAoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLmhpZGUoKVxuICAgICAgICB9KTtcblxuICAgICAgICAvLyDmlrDlop7vvJrorr7nva7liIfmjaLmjInpkq7ngrnlh7vkuovku7ZcbiAgICAgICAgaWYgKHRoaXMuc3dpdGNoQnV0dG9uKSB7XG4gICAgICAgICAgICBUb29scy5zZXRUb3VjaEV2ZW50KHRoaXMuc3dpdGNoQnV0dG9uLCAoKSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy50b2dnbGVSdWxlVHlwZSgpOyAvLyDliIfmjaLop4TliJnnsbvlnotcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5paw5aKe77ya5Yid5aeL5YyW5pi+56S654q25oCB77yI6buY6K6k5pi+56S65Y2V5py66KeE5YiZ77yJXG4gICAgICAgIHRoaXMuaW5pdGlhbGl6ZVJ1bGVEaXNwbGF5KCk7XG5cbiAgICAvLyAgICAgdGhpcy5jb250ZW50TGF5LnJlbW92ZUFsbENoaWxkcmVuKClcblxuICAgIC8vICAgICB0aGlzLmdldFRpdGxlTm9kZSh0aGlzLnRpdGxlTGlzdFswXSlcbiAgICAvLyAgICAgdGhpcy50aXBzTGlzdC5mb3JFYWNoKCh0aXRsZSwgaW5kZXgpID0+IHtcbiAgICAvLyAgICAgICAgIGxldCBpbmZvSXRlbSA9IGNjLmluc3RhbnRpYXRlKHRoaXMuaW5mb0l0ZW0xKTsvL+WIneWni+WMluS4gOS4qumihOWItuS9k1xuICAgIC8vICAgICAgICAgbGV0IGluZm9JdGVtT25lQ29udHJvbGxlciA9IGluZm9JdGVtLmdldENvbXBvbmVudChJbmZvSXRlbU9uZUNvbnRyb2xsZXIpXG4gICAgLy8gICAgICAgICBpbmZvSXRlbU9uZUNvbnRyb2xsZXIuc2V0RGF0YSh0aXRsZSlcbiAgICAvLyAgICAgICAgIHRoaXMuY29udGVudExheS5hZGRDaGlsZChpbmZvSXRlbSlcbiAgICAvLyAgICAgfSlcblxuICAgIC8vICAgICB0aGlzLmdldFRpdGxlTm9kZSh0aGlzLnRpdGxlTGlzdFsxXSlcbiAgICAvLyAgICAgdGhpcy5nZW5lcmF0aW9uTWV0aG9kTGlzdC5mb3JFYWNoKCh0aXRsZSwgaW5kZXgpID0+IHtcbiAgICAvLyAgICAgICAgIGxldCBpbmZvSXRlbSA9IGNjLmluc3RhbnRpYXRlKHRoaXMuaW5mb0l0ZW0xKTsvL+WIneWni+WMluS4gOS4qumihOWItuS9k1xuICAgIC8vICAgICAgICAgbGV0IGluZm9JdGVtT25lQ29udHJvbGxlciA9IGluZm9JdGVtLmdldENvbXBvbmVudChJbmZvSXRlbU9uZUNvbnRyb2xsZXIpXG4gICAgLy8gICAgICAgICBpbmZvSXRlbU9uZUNvbnRyb2xsZXIuc2V0RGF0YSh0aXRsZSlcblxuICAgIC8vICAgICAgICAgbGV0IGluZm9JbWcgPSBjYy5pbnN0YW50aWF0ZSh0aGlzLmluZm9JbWFnZUxpc3RbaW5kZXhdKVxuICAgIC8vICAgICAgICAgaW5mb0l0ZW1PbmVDb250cm9sbGVyLnNldGltZ05vZGUoaW5mb0ltZylcblxuICAgIC8vICAgICAgICAgdGhpcy5jb250ZW50TGF5LmFkZENoaWxkKGluZm9JdGVtKVxuICAgIC8vICAgICB9KVxuICAgIC8vICAgICB0aGlzLmdldFRpdGxlTm9kZSh0aGlzLnRpdGxlTGlzdFsyXSlcbiAgICAvLyAgICAgdGhpcy5wZXJtYW5lbnRMaXN0LmZvckVhY2goKHRpdGxlLCBpbmRleCkgPT4ge1xuICAgIC8vICAgICAgICAgbGV0IGluZm9JdGVtID0gY2MuaW5zdGFudGlhdGUodGhpcy5pbmZvSXRlbTEpOy8v5Yid5aeL5YyW5LiA5Liq6aKE5Yi25L2TXG4gICAgLy8gICAgICAgICBsZXQgaW5mb0l0ZW1PbmVDb250cm9sbGVyID0gaW5mb0l0ZW0uZ2V0Q29tcG9uZW50KEluZm9JdGVtT25lQ29udHJvbGxlcilcbiAgICAvLyAgICAgICAgIGluZm9JdGVtT25lQ29udHJvbGxlci5zZXREYXRhKHRpdGxlKVxuICAgIC8vICAgICAgICAgdGhpcy5jb250ZW50TGF5LmFkZENoaWxkKGluZm9JdGVtKVxuICAgIC8vICAgICB9KVxuICAgIC8vICAgICBsZXQgaW5mb0ltZzEgPSBjYy5pbnN0YW50aWF0ZSh0aGlzLmluZm9JbWFnZTEpXG4gICAgLy8gICAgIHRoaXMuY29udGVudExheS5hZGRDaGlsZChpbmZvSW1nMSlcblxuXG4gICAgLy8gICAgIHRoaXMuZ2V0VGl0bGVOb2RlKHRoaXMudGl0bGVMaXN0WzNdKVxuICAgIC8vICAgICB0aGlzLnJhbmRvbUxpc3QuZm9yRWFjaCgodGl0bGUsIGluZGV4KSA9PiB7XG4gICAgLy8gICAgICAgICBsZXQgaW5mb0l0ZW0gPSBjYy5pbnN0YW50aWF0ZSh0aGlzLmluZm9JdGVtMSk7Ly/liJ3lp4vljJbkuIDkuKrpooTliLbkvZNcbiAgICAvLyAgICAgICAgIGxldCBpbmZvSXRlbU9uZUNvbnRyb2xsZXIgPSBpbmZvSXRlbS5nZXRDb21wb25lbnQoSW5mb0l0ZW1PbmVDb250cm9sbGVyKVxuICAgIC8vICAgICAgICAgaW5mb0l0ZW1PbmVDb250cm9sbGVyLnNldERhdGEodGl0bGUpXG4gICAgLy8gICAgICAgICB0aGlzLmNvbnRlbnRMYXkuYWRkQ2hpbGQoaW5mb0l0ZW0pXG4gICAgLy8gICAgIH0pXG4gICAgLy8gICAgIGxldCBpbmZvSW1nMiA9IGNjLmluc3RhbnRpYXRlKHRoaXMuaW5mb0ltYWdlMilcbiAgICAvLyAgICAgdGhpcy5jb250ZW50TGF5LmFkZENoaWxkKGluZm9JbWcyKVxuXG4gICAgLy8gICAgIHRoaXMuZ2V0VGl0bGVOb2RlKHRoaXMudGl0bGVMaXN0WzRdKVxuICAgIC8vICAgICB0aGlzLmNoYWluc0xpc3QuZm9yRWFjaCgodGl0bGUsIGluZGV4KSA9PiB7XG4gICAgLy8gICAgICAgICBsZXQgaW5mb0l0ZW0gPSBjYy5pbnN0YW50aWF0ZSh0aGlzLmluZm9JdGVtMSk7Ly/liJ3lp4vljJbkuIDkuKrpooTliLbkvZNcbiAgICAvLyAgICAgICAgIGxldCBpbmZvSXRlbU9uZUNvbnRyb2xsZXIgPSBpbmZvSXRlbS5nZXRDb21wb25lbnQoSW5mb0l0ZW1PbmVDb250cm9sbGVyKVxuICAgIC8vICAgICAgICAgaW5mb0l0ZW1PbmVDb250cm9sbGVyLnNldERhdGEodGl0bGUpXG4gICAgLy8gICAgICAgICB0aGlzLmNvbnRlbnRMYXkuYWRkQ2hpbGQoaW5mb0l0ZW0pXG4gICAgLy8gICAgIH0pXG4gICAgLy8gICAgIHRoaXMuZ2V0VGl0bGVOb2RlKHRoaXMudGl0bGVMaXN0WzVdKVxuICAgIC8vICAgICB0aGlzLmljZUxpc3QuZm9yRWFjaCgodGl0bGUsIGluZGV4KSA9PiB7XG4gICAgLy8gICAgICAgICBsZXQgaW5mb0l0ZW0gPSBjYy5pbnN0YW50aWF0ZSh0aGlzLmluZm9JdGVtMSk7Ly/liJ3lp4vljJbkuIDkuKrpooTliLbkvZNcbiAgICAvLyAgICAgICAgIGxldCBpbmZvSXRlbU9uZUNvbnRyb2xsZXIgPSBpbmZvSXRlbS5nZXRDb21wb25lbnQoSW5mb0l0ZW1PbmVDb250cm9sbGVyKVxuICAgIC8vICAgICAgICAgaW5mb0l0ZW1PbmVDb250cm9sbGVyLnNldERhdGEodGl0bGUpXG4gICAgLy8gICAgICAgICB0aGlzLmNvbnRlbnRMYXkuYWRkQ2hpbGQoaW5mb0l0ZW0pXG4gICAgLy8gICAgIH0pXG4gICAgLy8gICAgIHRoaXMuZ2V0VGl0bGVOb2RlKHRoaXMudGl0bGVMaXN0WzZdKVxuICAgIC8vICAgICB0aGlzLnNjb3JpbmdEZXRhaWxzLmZvckVhY2goKHRpdGxlLCBpbmRleCkgPT4ge1xuICAgIC8vICAgICAgICAgbGV0IGluZm9JdGVtID0gY2MuaW5zdGFudGlhdGUodGhpcy5pbmZvSXRlbTEpOy8v5Yid5aeL5YyW5LiA5Liq6aKE5Yi25L2TXG4gICAgLy8gICAgICAgICBsZXQgaW5mb0l0ZW1PbmVDb250cm9sbGVyID0gaW5mb0l0ZW0uZ2V0Q29tcG9uZW50KEluZm9JdGVtT25lQ29udHJvbGxlcilcbiAgICAvLyAgICAgICAgIGluZm9JdGVtT25lQ29udHJvbGxlci5zZXREYXRhKHRpdGxlKVxuICAgIC8vICAgICAgICAgdGhpcy5jb250ZW50TGF5LmFkZENoaWxkKGluZm9JdGVtKVxuICAgIC8vICAgICB9KVxuICAgIC8vIH1cblxuXG4gICAgLy8gZ2V0VGl0bGVOb2RlKHRpdGxlOiBzdHJpbmcpIHtcbiAgICAvLyAgICAgbGV0IGluZm9JdGVtID0gY2MuaW5zdGFudGlhdGUodGhpcy5pbmZvSXRlbSk7Ly/liJ3lp4vljJbkuIDkuKrpooTliLbkvZNcbiAgICAvLyAgICAgbGV0IGluZm9JdGVtQ29udHJvbGxlciA9IGluZm9JdGVtLmdldENvbXBvbmVudChJbmZvSXRlbUNvbnRyb2xsZXIpXG4gICAgLy8gICAgIGluZm9JdGVtQ29udHJvbGxlci5zZXRDb250ZW50KHRpdGxlKVxuICAgIC8vICAgICB0aGlzLmNvbnRlbnRMYXkuYWRkQ2hpbGQoaW5mb0l0ZW0pXG4gICAgLy8gfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOaWsOWinu+8muWIneWni+WMluinhOWImeaYvuekuueKtuaAgVxuICAgICAqL1xuICAgIHByaXZhdGUgaW5pdGlhbGl6ZVJ1bGVEaXNwbGF5KCkge1xuICAgICAgICBpZiAodGhpcy5kYW5qaVNjcm9sbFZpZXcgJiYgdGhpcy5kdW9yZW5TY3JvbGxWaWV3KSB7XG4gICAgICAgICAgICAvLyDpu5jorqTmmL7npLrljZXmnLrop4TliJnvvIzpmpDol4/ogZTmnLrop4TliJlcbiAgICAgICAgICAgIHRoaXMuZGFuamlTY3JvbGxWaWV3LmFjdGl2ZSA9IHRydWU7XG4gICAgICAgICAgICB0aGlzLmR1b3JlblNjcm9sbFZpZXcuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICB0aGlzLmN1cnJlbnRSdWxlVHlwZSA9IDA7XG5cbiAgICAgICAgICAgIC8vIOiuvue9ruaMiemSruWIneWni+S9jee9ru+8iOW3pui+ueS9jee9ru+8iVxuICAgICAgICAgICAgaWYgKHRoaXMuc3dpdGNoQnV0dG9uKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zd2l0Y2hCdXR0b24ucG9zaXRpb24gPSBjYy52MygtMTUwLCAtMiwgMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmlrDlop7vvJrliIfmjaLop4TliJnnsbvlnovvvIjlnKjljZXmnLrlkozogZTmnLrkuYvpl7TliIfmjaLvvIlcbiAgICAgKi9cbiAgICBwcml2YXRlIHRvZ2dsZVJ1bGVUeXBlKCkge1xuICAgICAgICAvLyDliIfmjaLliLDlj6bkuIDnp43nsbvlnotcbiAgICAgICAgdGhpcy5jdXJyZW50UnVsZVR5cGUgPSB0aGlzLmN1cnJlbnRSdWxlVHlwZSA9PT0gMCA/IDEgOiAwO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKGDliIfmjaLliLA6ICR7dGhpcy5jdXJyZW50UnVsZVR5cGUgPT09IDAgPyAn5Y2V5py66KeE5YiZJyA6ICfogZTmnLrop4TliJknfWApO1xuXG4gICAgICAgIC8vIOenu+WKqOaMiemSruS9jee9rlxuICAgICAgICB0aGlzLm1vdmVCdXR0b25Ub1Bvc2l0aW9uKCk7XG5cbiAgICAgICAgLy8g5YiH5o2iU2Nyb2xsVmlld+aYvuekulxuICAgICAgICB0aGlzLnN3aXRjaFNjcm9sbFZpZXdEaXNwbGF5KCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5paw5aKe77ya56e75Yqo5oyJ6ZKu5Yiw5oyH5a6a5L2N572uXG4gICAgICovXG4gICAgcHJpdmF0ZSBtb3ZlQnV0dG9uVG9Qb3NpdGlvbigpIHtcbiAgICAgICAgaWYgKCF0aGlzLnN3aXRjaEJ1dHRvbikge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5oyJ6ZKu5L2N572u77ya5bem6L6577yILTE1MO+8jC0y77yJ5Y+z6L6577yIMTQy77yMLTLvvIlcbiAgICAgICAgY29uc3QgbGVmdFBvc2l0aW9uID0gY2MudjMoLTE1MCwgLTIsIDApO1xuICAgICAgICBjb25zdCByaWdodFBvc2l0aW9uID0gY2MudjMoMTQyLCAtMiwgMCk7XG5cbiAgICAgICAgY29uc3QgdGFyZ2V0UG9zaXRpb24gPSB0aGlzLmN1cnJlbnRSdWxlVHlwZSA9PT0gMCA/IGxlZnRQb3NpdGlvbiA6IHJpZ2h0UG9zaXRpb247XG5cbiAgICAgICAgLy8g5L2/55So5Yqo55S756e75Yqo5oyJ6ZKuXG4gICAgICAgIGNjLnR3ZWVuKHRoaXMuc3dpdGNoQnV0dG9uKVxuICAgICAgICAgICAgLnRvKDAuMywgeyBwb3NpdGlvbjogdGFyZ2V0UG9zaXRpb24gfSwgeyBlYXNpbmc6ICdxdWFydE91dCcgfSlcbiAgICAgICAgICAgIC5zdGFydCgpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOaWsOWinu+8muWIh+aNolNjcm9sbFZpZXfmmL7npLpcbiAgICAgKi9cbiAgICBwcml2YXRlIHN3aXRjaFNjcm9sbFZpZXdEaXNwbGF5KCkge1xuICAgICAgICBpZiAoIXRoaXMuZGFuamlTY3JvbGxWaWV3IHx8ICF0aGlzLmR1b3JlblNjcm9sbFZpZXcpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICh0aGlzLmN1cnJlbnRSdWxlVHlwZSA9PT0gMCkge1xuICAgICAgICAgICAgLy8g5pi+56S65Y2V5py66KeE5YiZXG4gICAgICAgICAgICB0aGlzLmRhbmppU2Nyb2xsVmlldy5hY3RpdmUgPSB0cnVlO1xuICAgICAgICAgICAgdGhpcy5kdW9yZW5TY3JvbGxWaWV3LmFjdGl2ZSA9IGZhbHNlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8g5pi+56S66IGU5py66KeE5YiZXG4gICAgICAgICAgICB0aGlzLmRhbmppU2Nyb2xsVmlldy5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICAgIHRoaXMuZHVvcmVuU2Nyb2xsVmlldy5hY3RpdmUgPSB0cnVlO1xuICAgICAgICB9XG4gICAgfVxuXG5cblxuICAgIHNob3coYmFja0NhbGxiYWNrOiBGdW5jdGlvbikge1xuICAgICAgICB0aGlzLmJhY2tDYWxsYmFjayA9IGJhY2tDYWxsYmFja1xuICAgICAgICB0aGlzLm5vZGUuYWN0aXZlID0gdHJ1ZVxuICAgICAgICB0aGlzLmJvYXJkQmcuc2NhbGUgPSAwXG4gICAgICAgIC8vIOaJp+ihjOe8qeaUvuWKqOeUu1xuICAgICAgICBjYy50d2Vlbih0aGlzLmJvYXJkQmcpXG4gICAgICAgICAgICAudG8oQ29uZmlnLmRpYWxvZ1NjYWxlVGltZSwgeyBzY2FsZTogMSB9KVxuICAgICAgICAgICAgLnN0YXJ0KCk7XG4gICAgfVxuICAgIGhpZGUoKSB7XG4gICAgICAgIGlmICh0aGlzLmJhY2tDYWxsYmFjaykge1xuICAgICAgICAgICAgdGhpcy5iYWNrQ2FsbGJhY2soKVxuICAgICAgICB9XG4gICAgICAgIC8vIOaJp+ihjOe8qeaUvuWKqOeUu1xuICAgICAgICBjYy50d2Vlbih0aGlzLmJvYXJkQmcpXG4gICAgICAgICAgICAudG8oQ29uZmlnLmRpYWxvZ1NjYWxlVGltZSwgeyBzY2FsZTogMCB9KVxuICAgICAgICAgICAgLmNhbGwoKCkgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMubm9kZS5hY3RpdmUgPSBmYWxzZVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIC5zdGFydCgpO1xuICAgIH1cblxuICAgIC8vIHVwZGF0ZSAoZHQpIHt9XG59Il19