
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/tools/MeshSdkApi":3,"./assets/scripts/bean/GlobalBean":4,"./assets/scripts/game/Chess/GridController":5,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/meshTools/Singleton":7,"./assets/resources/i18n/en":8,"./assets/scripts/common/GameMgr":9,"./assets/scripts/net/GameServerUrl":10,"./assets/scripts/level/LevelPageController":11,"./assets/scripts/pfb/MatchItemController":12,"./assets/scripts/test/NoticeRoundStartTest":13,"./assets/meshTools/BaseSDK":14,"./assets/scripts/GlobalManagerController":15,"./assets/scripts/ToastController":16,"./assets/scripts/bean/GameBean":17,"./assets/meshTools/tools/MeshSdk":18,"./assets/scripts/common/MineConsole":19,"./assets/meshTools/tools/Publish":20,"./assets/scripts/util/AudioMgr":21,"./assets/scripts/TipsDialogController":22,"./assets/scripts/bean/EnumBean":23,"./assets/scripts/game/CongratsDialogController":24,"./assets/scripts/bean/LanguageType":25,"./assets/scripts/start_up/StartUpCenterController":26,"./assets/scripts/game/GameScoreController":27,"./assets/scripts/game/BtnController":28,"./assets/scripts/common/EventCenter":29,"./assets/scripts/game/Chess/HexChessBoardController":30,"./assets/scripts/common/GameTools":31,"./assets/scripts/hall/HallPageController":32,"./assets/scripts/hall/HallCreateRoomController":33,"./assets/scripts/hall/HallParentController":34,"./assets/scripts/hall/HallJoinRoomController":35,"./assets/scripts/hall/InfoDialogController":36,"./assets/scripts/hall/MatchParentController":37,"./assets/scripts/hall/LevelSelectDemo":38,"./assets/scripts/hall/PlayerLayoutController":39,"./assets/scripts/hall/HallCenterLayController":40,"./assets/scripts/game/Chess/ChessBoardController":41,"./assets/scripts/hall/TopUpDialogController":42,"./assets/scripts/hall/SettingDialogController":43,"./assets/scripts/hall/KickOutDialogController":44,"./assets/scripts/hall/HallAutoController":45,"./assets/scripts/hall/Level/LevelSelectExample":46,"./assets/scripts/hall/Level/LevelSelectPageController":47,"./assets/scripts/hall/Level/ScrollViewHelper":48,"./assets/scripts/hall/LeaveDialogController":49,"./assets/scripts/net/HttpUtils":50,"./assets/scripts/net/HttpManager":51,"./assets/scripts/hall/Level/LevelItemController":52,"./assets/scripts/net/WebSocketTool":53,"./assets/scripts/net/ErrorCode":54,"./assets/scripts/net/IHttpMsgBody":55,"./assets/scripts/net/WebSocketManager":56,"./assets/scripts/net/MessageId":57,"./assets/scripts/pfb/InfoItemController":58,"./assets/scripts/common/GameData":59,"./assets/scripts/pfb/InfoItemOneController":60,"./assets/scripts/pfb/PlayerGameController ":61,"./assets/scripts/pfb/SeatItemController":62,"./assets/scripts/hall/Level/LevelSelectDemoInLevel":63,"./assets/scripts/pfb/CongratsItemController":64,"./assets/scripts/pfb/PlayerScoreController":65,"./assets/scripts/util/Config":66,"./assets/scripts/util/LocalStorageManager":67,"./assets/scripts/util/AudioManager":68,"./assets/resources/i18n/zh_HK":69,"./assets/scripts/util/BlockingQueue":70,"./assets/scripts/util/Tools":71,"./assets/scripts/net/MessageBaseBean":72,"./assets/scripts/util/Dictionary":73,"./assets/scripts/start_up/StartUpPageController":74,"./assets/meshTools/MeshTools":75,"./assets/scripts/util/NickNameLabel":76,"./assets/resources/i18n/zh_CN":77,"./assets/scripts/game/GamePageController":78},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{"../MeshTools":75,"../BaseSDK":14,"../../scripts/net/MessageBaseBean":72,"../../scripts/common/GameMgr":9,"../../scripts/common/EventCenter":29,"MeshSdk":18},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{"../../meshTools/Singleton":7,"../hall/HallAutoController":45},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"./ScrollViewHelper":48},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":3,"./EventCenter":29,"./GameData":59,"./GameTools":31,"./MineConsole":19},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{"../net/MessageId":57,"../net/WebSocketManager":56,"../hall/LeaveDialogController":49,"../util/Tools":71,"../util/Config":66},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{"../util/NickNameLabel":76,"../util/Tools":71},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"../common/GameMgr":9,"../common/EventCenter":29,"../net/MessageId":57},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{"../meshTools/MeshTools":75,"../meshTools/tools/Publish":20,"./bean/EnumBean":23,"./bean/GlobalBean":4,"./bean/LanguageType":25,"./common/EventCenter":29,"./common/GameMgr":9,"./game/GamePageController":78,"./hall/HallPageController":32,"./level/LevelPageController":11,"./hall/TopUpDialogController":42,"./net/ErrorCode":54,"./net/GameServerUrl":10,"./net/MessageBaseBean":72,"./net/MessageId":57,"./net/WebSocketManager":56,"./net/WebSocketTool":53,"./start_up/StartUpPageController":74,"./TipsDialogController":22,"./ToastController":16,"./util/AudioMgr":21,"./util/Config":66},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{"../../meshTools/Singleton":7},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"../Singleton":7},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{"./Config":66,"./Dictionary":73},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{"./util/Config":66,"./util/Tools":71},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"../bean/GlobalBean":4,"../common/EventCenter":29,"../common/GameMgr":9,"../net/MessageBaseBean":72,"../pfb/CongratsItemController":64,"../util/Config":66,"../util/Tools":71},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"../common/EventCenter":29,"../common/GameMgr":9,"../net/MessageBaseBean":72,"../util/Config":66},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{"../bean/GlobalBean":4,"../pfb/PlayerScoreController":65},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../util/AudioManager":68,"../util/Config":66,"../util/LocalStorageManager":67},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../../meshTools/Singleton":7,"./GameMgr":9},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"../../bean/GlobalBean":4,"../../pfb/PlayerGameController ":61},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"../../meshTools/Singleton":7},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{"../bean/GlobalBean":4,"../common/GameMgr":9,"../net/MessageId":57,"../net/WebSocketManager":56,"../net/WebSocketTool":53,"../ToastController":16,"../util/AudioManager":68,"./HallParentController":34,"./InfoDialogController":36,"./KickOutDialogController":44,"./LeaveDialogController":49,"./Level/LevelSelectPageController":47,"./MatchParentController":37,"./SettingDialogController":43},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../bean/GlobalBean":4,"../pfb/SeatItemController":62,"../util/Config":66,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../../meshTools/tools/Publish":20,"../bean/GlobalBean":4,"../common/GameMgr":9,"../net/MessageId":57,"../net/WebSocketManager":56,"../ToastController":16,"../util/Config":66,"../util/Tools":71,"./HallCenterLayController":40},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../util/Config":66,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"../../meshTools/tools/Publish":20,"../bean/GlobalBean":4,"../common/EventCenter":29,"../common/GameMgr":9,"../net/MessageBaseBean":72,"../pfb/MatchItemController":12,"../util/Config":66,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../bean/GlobalBean":4,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../bean/GlobalBean":4,"../net/MessageId":57,"../net/WebSocketManager":56,"../ToastController":16,"./HallAutoController":45,"./HallCreateRoomController":33,"./HallJoinRoomController":35},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../../bean/GlobalBean":4,"../../pfb/PlayerGameController ":61},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../common/GameMgr":9,"../util/Config":66,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"../../meshTools/tools/Publish":20,"../util/AudioManager":68,"../util/Config":66,"../util/LocalStorageManager":67,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../net/MessageId":57,"../net/WebSocketManager":56,"../util/Config":66,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"../bean/GlobalBean":4,"../util/Config":66,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{"../../GlobalManagerController":15,"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"../common/GameMgr":9,"../net/MessageId":57,"../net/WebSocketManager":56,"../util/Config":66,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{"./HttpUtils":50,"./MessageBaseBean":72,"./GameServerUrl":10,"../../meshTools/MeshTools":75,"../common/GameMgr":9,"../common/EventCenter":29},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"./MessageBaseBean":72,"./MessageId":57,"../util/Tools":71,"../../meshTools/Singleton":7,"../common/EventCenter":29,"../common/GameMgr":9},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{"../../meshTools/Singleton":7,"../common/EventCenter":29,"../common/GameMgr":9,"./WebSocketTool":53},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../../meshTools/MeshTools":75,"../../meshTools/Singleton":7,"../net/GameServerUrl":10},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"../util/Tools":71},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../util/NickNameLabel":76,"../util/Tools":71},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectDemoInLevel.js"},{"deps":{"../../meshTools/tools/Publish":20,"../util/Config":66,"../util/NickNameLabel":76,"../util/Tools":71},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{"../bean/GlobalBean":4,"../util/NickNameLabel":76,"../util/Tools":71},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../../meshTools/Singleton":7},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{"./AudioMgr":21,"./LocalStorageManager":67},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"./AudioManager":68,"./Config":66},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{"../common/GameMgr":9,"./StartUpCenterController":26},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"./tools/Publish":20},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{"../bean/GlobalBean":4,"../hall/LeaveDialogController":49,"../util/AudioManager":68,"../util/Config":66,"../util/Tools":71,"./CongratsDialogController":24,"./GameScoreController":27,"./Chess/ChessBoardController":41,"./Chess/HexChessBoardController":30,"../net/WebSocketManager":56,"../net/MessageId":57},"path":"preview-scripts/assets/scripts/game/GamePageController.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    